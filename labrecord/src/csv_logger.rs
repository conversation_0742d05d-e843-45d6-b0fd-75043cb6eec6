use anyhow::{Context, Result};
use std::fs;
use std::path::Path;
use std::sync::Arc;
use tokio::fs::File;
use tokio::io::{AsyncWriteExt, BufWriter};
use tokio::sync::Mutex;

use crate::sample::Sample;

/// A simple CSV file logger for single sensor data
#[derive(Clone)]
pub struct CsvLogger {
    file: Arc<Mutex<BufWriter<File>>>,
}

impl CsvLogger {
    /// Create a new CSV logger for a single sensor
    pub async fn new(path: &Path) -> Result<Self> {
        // Ensure the directory exists
        if let Some(parent) = Path::new(path).parent() {
            fs::create_dir_all(parent).context("Failed to create directory")?;
        }

        // Create the file
        let mut file = File::create(path)
            .await
            .context("Failed to create CSV file")?;

        // Write the header for single sensor
        file.write_all(b"time_offset,x,y,z\n")
            .await
            .context("Failed to write CSV header")?;
        let file = BufWriter::new(file);

        Ok(Self {
            file: Arc::new(Mutex::new(file)),
        })
    }

    /// Log a single sample to the CSV file
    pub async fn log(
        &self,
        fmt_buffer: &mut String,
        time_epoch: u128,
        sample: &Sample,
    ) -> Result<()> {
        use std::fmt::Write;

        fmt_buffer.clear();
        write!(
            fmt_buffer,
            "{},{},{},{}\n",
            time_epoch, sample.x, sample.y, sample.z,
        )
        .context("Failed to format sample")?;

        self.file
            .lock()
            .await
            .write_all(fmt_buffer.as_bytes())
            .await
            .context("Failed to write sample to CSV file")?;

        Ok(())
    }

    /// Flush the CSV writer
    pub async fn flush(&self) -> Result<()> {
        self.file
            .lock()
            .await
            .flush()
            .await
            .context("Failed to flush CSV file")?;
        Ok(())
    }
}
