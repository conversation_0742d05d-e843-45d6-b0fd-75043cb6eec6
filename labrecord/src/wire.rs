use anyhow::Result;

use crate::interface::uart_packet_t;

pub fn parse_uart_packet(buffer: &[u8]) -> Result<uart_packet_t> {
    if buffer.len() != std::mem::size_of::<uart_packet_t>() {
        anyhow::bail!(
            "Invalid packet size: expected {}, got {}",
            std::mem::size_of::<uart_packet_t>(),
            buffer.len()
        );
    }

    let packet = unsafe { std::ptr::read_unaligned(buffer.as_ptr() as *const uart_packet_t) };

    if packet.header != [b'A', b'D', b'X', b'L'] {
        anyhow::bail!(
            "Invalid packet header: expected 'ADXL', got {:02X?} ('{}')",
            packet.header,
            String::from_utf8_lossy(&packet.header)
        );
    }

    if packet.checksum != calculate_checksum(&buffer[..std::mem::size_of::<uart_packet_t>() - 2]) {
        anyhow::bail!("Invalid packet checksum");
    }

    Ok(packet)
}

fn calculate_checksum(data: &[u8]) -> u16 {
    data.iter().fold(0, |acc, x| acc + *x as u16)
}
