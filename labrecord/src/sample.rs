use crate::interface::uart_packet_t;

/// A sample from the ADXL345 accelerometer
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Sample {
    pub x: i16,
    pub y: i16,
    pub z: i16,
}

/// Convert UartPacket accelerometer data to Sample vector
pub fn packet_to_samples(packet: &uart_packet_t) -> Vec<Sample> {
    let sample_count = packet.sample_count as usize;
    let mut samples = Vec::with_capacity(sample_count);
    for i in 0..sample_count {
        // Copy values to avoid packed struct alignment issues
        let x = packet.accel[i][0];
        let y = packet.accel[i][1];
        let z = packet.accel[i][2];
        samples.push(Sample { x, y, z });
    }
    samples
}
