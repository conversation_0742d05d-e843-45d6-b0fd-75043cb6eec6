use std::sync::{
    atomic::{AtomicUsize, Ordering},
    Arc,
};

use eframe::egui::{self, Color32};
use egui_plot::{Line, Plot, PlotPoint};
use tokio::sync::Mutex;

use crate::fft_analysis::{FftResult, SampleBuffer};
use crate::interface::SAMPLES_PER_SECOND;

type Points = Vec<PlotPoint>;

pub fn run_gui(app: LabrecordApp) {
    let options = eframe::NativeOptions::default();
    eframe::run_native(
        "Sensor Readings",
        options,
        Box::new(|_cc| Ok(Box::new(app))),
    )
    .unwrap();
}

#[derive(Debug, Clone)]
pub struct SensorData {
    pub x: Points,
    pub y: Points,
    pub z: Points,
    pub fft_buffer: SampleBuffer,
    pub fft_result: Option<FftResult>,
    pub fft_frequencies_x: Points, // For X-axis frequency domain plot
    pub fft_frequencies_y: Points, // For Y-axis frequency domain plot
    pub fft_frequencies_z: Points, // For Z-axis frequency domain plot
    pub last_analysis_time: std::time::Instant,
}

impl Default for SensorData {
    fn default() -> Self {
        Self {
            x: Vec::new(),
            y: Vec::new(),
            z: Vec::new(),
            fft_buffer: SampleBuffer::new(),
            fft_result: None,
            fft_frequencies_x: Vec::new(),
            fft_frequencies_y: Vec::new(),
            fft_frequencies_z: Vec::new(),
            last_analysis_time: std::time::Instant::now(),
        }
    }
}

impl SensorData {
    pub fn truncate(&mut self, num: usize) {
        if self.x.len() > num {
            self.x.drain(0..self.x.len() - num);
            self.y.drain(0..self.y.len() - num);
            self.z.drain(0..self.z.len() - num);
        }
    }
}

#[derive(Clone)]
pub struct LabrecordApp {
    pub sensor1: Arc<Mutex<SensorData>>,
    pub sensor2: Arc<Mutex<SensorData>>,
    pub history_limit: Arc<AtomicUsize>,
}

impl Default for LabrecordApp {
    fn default() -> Self {
        Self {
            sensor1: Arc::new(Mutex::new(SensorData::default())),
            sensor2: Arc::new(Mutex::new(SensorData::default())),
            history_limit: Arc::new(AtomicUsize::new(SAMPLES_PER_SECOND as usize * 10)),
        }
    }
}

impl eframe::App for LabrecordApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        egui::CentralPanel::default().show(ctx, |ui| {
            let mut sensor1 = self.sensor1.blocking_lock();
            let mut sensor2 = self.sensor2.blocking_lock();
            let mut history_limit_text = self.history_limit.load(Ordering::Relaxed).to_string();
            if ui
                .add(
                    egui::TextEdit::singleline(&mut history_limit_text)
                        .hint_text("History Limit")
                        .desired_width(100.0),
                )
                .changed()
            {
                if let Ok(new_limit) = history_limit_text.parse::<usize>() {
                    self.history_limit
                        .store(new_limit, std::sync::atomic::Ordering::Relaxed);
                }
            }
            let history_limit = self.history_limit.load(Ordering::Relaxed);
            sensor1.truncate(history_limit);
            sensor2.truncate(history_limit);

            // Display dominant frequencies as text for all axes
            ui.horizontal(|ui| {
                ui.vertical(|ui| {
                    ui.label("Sensor 1 Dominant Frequencies (Hz):");
                    if let Some(ref fft_result) = sensor1.fft_result {
                        ui.label("X-axis:");
                        for (i, (freq, magnitude)) in
                            fft_result.x.dominant_frequencies.iter().enumerate().take(3)
                        {
                            ui.label(format!("  {}. {:.1} Hz ({:.1})", i + 1, freq, magnitude));
                        }
                        ui.label("Y-axis:");
                        for (i, (freq, magnitude)) in
                            fft_result.y.dominant_frequencies.iter().enumerate().take(3)
                        {
                            ui.label(format!("  {}. {:.1} Hz ({:.1})", i + 1, freq, magnitude));
                        }
                        ui.label("Z-axis:");
                        for (i, (freq, magnitude)) in
                            fft_result.z.dominant_frequencies.iter().enumerate().take(3)
                        {
                            ui.label(format!("  {}. {:.1} Hz ({:.1})", i + 1, freq, magnitude));
                        }
                    } else {
                        ui.label("Analyzing...");
                    }
                });

                ui.vertical(|ui| {
                    ui.label("Sensor 2 Dominant Frequencies (Hz):");
                    if let Some(ref fft_result) = sensor2.fft_result {
                        ui.label("X-axis:");
                        for (i, (freq, magnitude)) in
                            fft_result.x.dominant_frequencies.iter().enumerate().take(3)
                        {
                            ui.label(format!("  {}. {:.1} Hz ({:.1})", i + 1, freq, magnitude));
                        }
                        ui.label("Y-axis:");
                        for (i, (freq, magnitude)) in
                            fft_result.y.dominant_frequencies.iter().enumerate().take(3)
                        {
                            ui.label(format!("  {}. {:.1} Hz ({:.1})", i + 1, freq, magnitude));
                        }
                        ui.label("Z-axis:");
                        for (i, (freq, magnitude)) in
                            fft_result.z.dominant_frequencies.iter().enumerate().take(3)
                        {
                            ui.label(format!("  {}. {:.1} Hz ({:.1})", i + 1, freq, magnitude));
                        }
                    } else {
                        ui.label("Analyzing...");
                    }
                });
            });

            let aspect_ratio = ui.available_width() / ui.available_height();

            ui.horizontal(|ui| {
                ui.vertical(|ui| {
                    ui.label("Time Domain");
                    Plot::new("Sensor 1")
                        .view_aspect(aspect_ratio)
                        .width(ui.available_width() / 2.0)
                        .default_y_bounds(-4096.0, 4096.0)
                        .show(ui, |plot_ui| {
                            plot_ui.line(Line::new("x", sensor1.x.as_slice()).color(Color32::RED));
                            plot_ui
                                .line(Line::new("y", sensor1.y.as_slice()).color(Color32::GREEN));
                            plot_ui.line(Line::new("z", sensor1.z.as_slice()).color(Color32::BLUE));
                        });
                    Plot::new("Sensor 2")
                        .view_aspect(aspect_ratio)
                        .width(ui.available_width() / 2.0)
                        .default_y_bounds(-4096.0, 4096.0)
                        .show(ui, |plot_ui| {
                            plot_ui.line(Line::new("x", sensor2.x.as_slice()).color(Color32::RED));
                            plot_ui
                                .line(Line::new("y", sensor2.y.as_slice()).color(Color32::GREEN));
                            plot_ui.line(Line::new("z", sensor2.z.as_slice()).color(Color32::BLUE));
                        });
                });

                ui.vertical(|ui| {
                    ui.label("Frequency Domain - X Axis");
                    Plot::new("Sensor 1 FFT")
                        .view_aspect(aspect_ratio)
                        .default_x_bounds(0.0, 200.0)
                        .default_y_bounds(0.0, 100.0)
                        .show(ui, |plot_ui| {
                            plot_ui.line(
                                Line::new("X spectrum", sensor1.fft_frequencies_x.as_slice())
                                    .color(Color32::RED),
                            );
                            plot_ui.line(
                                Line::new("Y spectrum", sensor1.fft_frequencies_y.as_slice())
                                    .color(Color32::GREEN),
                            );
                            plot_ui.line(
                                Line::new("Z spectrum", sensor1.fft_frequencies_z.as_slice())
                                    .color(Color32::BLUE),
                            );
                        });
                    Plot::new("Sensor 2 FFT")
                        .view_aspect(aspect_ratio)
                        .default_x_bounds(0.0, 200.0)
                        .default_y_bounds(0.0, 100.0)
                        .show(ui, |plot_ui| {
                            plot_ui.line(
                                Line::new("X spectrum", sensor2.fft_frequencies_x.as_slice())
                                    .color(Color32::RED),
                            );
                            plot_ui.line(
                                Line::new("Y spectrum", sensor2.fft_frequencies_y.as_slice())
                                    .color(Color32::GREEN),
                            );
                            plot_ui.line(
                                Line::new("Z spectrum", sensor2.fft_frequencies_z.as_slice())
                                    .color(Color32::BLUE),
                            );
                        });
                });
            });
        });

        ctx.request_repaint(); // Continuous refresh
    }
}
