use anyhow::{Context, Result};
use chrono::Local;
use clap::Parser;
use gethostname::gethostname;
use gui::LabrecordApp;
use interface::UART_BAUD_RATE;
use serde::Deserialize;
use std::fs;

use std::path::PathBuf;
use std::sync::atomic::AtomicBool;
use std::time::Duration;
use tokio::io::AsyncRead;
use tokio_serial::{self, SerialPortType};

use csv_logger::CsvLogger;

mod csv_logger;
mod fft_analysis;
mod gui;
mod interface;
mod sample;
mod serial;
mod wire;

#[derive(Parser, Debug)]
#[command(author, version, about = "Record ADXL345 accelerometer data")]
struct Cli {
    /// Disable the GUI and run in headless mode
    #[arg(long)]
    no_gui: bool,
}

#[derive(Debug, Deserialize)]
pub struct Config {
    port: String,
    scenario: String,
    defect: bool,
    defect_kind: Option<String>,
}

pub static RUNNING: AtomicBool = AtomicBool::new(true);

fn main() {
    let args = Cli::parse();

    // Set up Ctrl+C handler
    ctrlc::set_handler(move || {
        eprintln!("\nReceived Ctrl+C, shutting down...");
        RUNNING.store(false, std::sync::atomic::Ordering::SeqCst);
        std::thread::sleep(Duration::from_millis(1500));
        std::process::exit(0);
    })
    .context("Error setting Ctrl+C handler")
    .unwrap();

    if args.no_gui {
        // Run in headless mode
        println!("Running in headless mode (GUI disabled, FFT analysis disabled)");
        let rt = tokio::runtime::Runtime::new()
            .context("Failed to create Tokio runtime")
            .unwrap();
        rt.block_on(app_loop_headless());
    } else {
        // Run with GUI
        println!("Running with GUI enabled (FFT analysis enabled)");
        let gui = gui::LabrecordApp::default();
        let gui2 = gui.clone();

        std::thread::Builder::new()
            .name("data-recorder".to_string())
            .spawn(move || {
                let rt = tokio::runtime::Runtime::new()
                    .context("Failed to create Tokio runtime")
                    .unwrap();
                rt.block_on(app_loop(gui2));
            })
            .unwrap();

        gui::run_gui(gui);
    }

    RUNNING.store(false, std::sync::atomic::Ordering::SeqCst);
}

async fn app_loop(gui: LabrecordApp) {
    while let Err(e) = app(Some(gui.clone())).await {
        eprintln!("Error in main app: {:?}", e);
        tokio::time::sleep(Duration::from_secs(1)).await;
    }
}

async fn app_loop_headless() {
    while let Err(e) = app(None).await {
        eprintln!("Error in main app: {:?}", e);
        tokio::time::sleep(Duration::from_secs(1)).await;
    }
}

async fn app(gui: Option<LabrecordApp>) -> Result<()> {
    let _args = Cli::parse();
    let config = fs::read_to_string("config.toml").context("Failed to read config file")?;
    let config: Config = toml::from_str(&config).context("Failed to parse config file")?;

    let scenario_start = Local::now();
    let scenario_start = scenario_start.format("%Y-%m-%d_%H-%M-%S").to_string();

    'outer: while RUNNING.load(std::sync::atomic::Ordering::SeqCst) {
        // Connect to the serial port
        let stream: Box<dyn AsyncRead + Unpin + Send> = {
            println!(
                "Opening serial port {} at {UART_BAUD_RATE} baud",
                config.port
            );
            Box::new(
                match tokio_serial::SerialStream::open(&tokio_serial::new(
                    &config.port,
                    UART_BAUD_RATE,
                ))
                .context("Failed to open serial port")
                {
                    Ok(stream) => stream,
                    Err(e) => {
                        println!("Error opening serial port: {}", e);
                        let ports = tokio_serial::available_ports()?
                            .iter()
                            .filter(|p| matches!(p.port_type, SerialPortType::UsbPort(_)))
                            .map(|p| p.port_name.clone())
                            .collect::<Vec<_>>();
                        println!("Available ports: {:?}", ports);
                        tokio::time::sleep(Duration::from_secs(2)).await;
                        println!("Retrying...");
                        continue 'outer;
                    }
                },
            )
        };
        let stream = tokio::io::BufReader::new(stream);
        println!("Connected!");

        // Give the firmware some time to stabilize before starting data collection
        println!("Waiting 1 seconds for firmware to stabilize...");
        tokio::time::sleep(Duration::from_secs(1)).await;

        // Create separate CSV loggers for each sensor
        // ensure that we do not reuse the same filename
        let batch_start = Local::now();
        let batch_start = batch_start.format("%Y-%m-%d_%H-%M-%S").to_string();

        let sensor1_path = PathBuf::from(format!("readings/readings-{}-s1.csv", batch_start));
        let sensor2_path = PathBuf::from(format!("readings/readings-{}-s2.csv", batch_start));
        println!("Logging sensor 1 to {}", sensor1_path.display());
        println!("Logging sensor 2 to {}", sensor2_path.display());

        tokio::fs::write(
            PathBuf::from(format!("readings/metadata-{}.json", batch_start)),
            serde_json::json!({
                "scenario": config.scenario,
                "defect": config.defect,
                "defect_kind": config.defect_kind,
                "batch_start": batch_start,
                "scenario_start": scenario_start,
                "hostname": gethostname().to_string_lossy(),
                "source": config.port,
                "sensor1_file": sensor1_path.file_name().unwrap().to_string_lossy(),
                "sensor2_file": sensor2_path.file_name().unwrap().to_string_lossy(),
            })
            .to_string(),
        )
        .await
        .context("Failed to write metadata JSON")?;

        let csv_logger1 = CsvLogger::new(&sensor1_path).await?;
        let csv_logger2 = CsvLogger::new(&sensor2_path).await?;

        // Convert the BufReader to a Box for the new do_batch function
        let stream_box: Box<dyn AsyncRead + Unpin + Send> = Box::new(stream);

        if let Err(e) = serial::do_batch(stream_box, csv_logger1, csv_logger2, gui.as_ref()).await {
            println!("Error while collecting batch: {}", e);
            continue 'outer;
        }

        println!("Batch complete");
    }

    println!("Shutting down...");

    Ok(())
}
