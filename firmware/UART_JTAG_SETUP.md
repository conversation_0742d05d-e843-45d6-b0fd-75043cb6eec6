# UART Data + JTAG Logging Configuration

## Overview
This firmware has been configured to use **USB-JTAG for logging** and **UART0 for high-speed binary sensor data transmission**. This eliminates conflicts between log messages and sensor data.

## Configuration Changes Made

### 1. UART0 Configuration (Data Only)
- **Purpose**: Exclusive binary sensor data transmission
- **Baud Rate**: 921,600 bps (high-speed)
- **Pins**: TX=43, RX=44
- **Data Format**: Binary packets with "ADXL" header
- **No logging interference**: Clean binary data stream

### 2. USB-JTAG Configuration (Logging Only)
- **Purpose**: All ESP_LOG output (ESP_LOGI, ESP_LOGW, ESP_LOGE, etc.)
- **Interface**: USB-JTAG built into ESP32-S3
- **Connection**: USB cable to development board
- **Advantage**: Full logging capability without interfering with data

### 3. sdkconfig Changes
```
# Primary console changed from UART to USB-JTAG
CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG=y
# CONFIG_ESP_CONSOLE_UART_DEFAULT is not set

# Secondary console disabled
CONFIG_ESP_CONSOLE_SECONDARY_NONE=y
# CONFIG_ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG is not set

# UART console disabled
# CONFIG_ESP_CONSOLE_UART is not set
CONFIG_ESP_CONSOLE_UART_NUM=-1
```

## Binary Data Packet Format

Each sensor burst (16 samples) is sent as a single binary packet:

```c
typedef struct __attribute__((packed)) {
    uint8_t header[4];               // "ADXL" magic header
    uint8_t sensor_id;               // Sensor ID (1 or 2)
    uint32_t timestamp;              // FreeRTOS tick timestamp
    uint8_t sample_count;            // Number of samples (16)
    int16_t accel[16][3];           // Raw accelerometer data (96 bytes)
    uint16_t checksum;               // Data integrity checksum
} uart_packet_t;
```

**Total packet size**: 108 bytes per burst
**Data rate**: ~1,600 packets/second (both sensors combined)
**Throughput**: ~173 KB/s of sensor data

## Hardware Connections

### UART0 (Data)
- **TX Pin 43** → Connect to your data receiver
- **RX Pin 44** → Not used for data transmission
- **GND** → Common ground with receiver

### USB-JTAG (Logging)
- **USB-C connector** on ESP32-S3 development board
- Connect to computer for logging via ESP-IDF monitor

## Usage Instructions

### 1. Building and Flashing
```bash
cd firmware
idf.py build
idf.py flash
```

### 2. Monitoring Logs (USB-JTAG)
```bash
idf.py monitor
```
This will show all ESP_LOG output through USB-JTAG.

### 3. Receiving Data (UART0)
Connect your data receiver to Pin 43 (TX) at 921,600 baud.
Parse binary packets starting with "ADXL" header.

### 4. Data Parsing Example (Python)
```python
import struct
import serial

ser = serial.Serial('/dev/ttyUSB0', 921600)

while True:
    # Look for "ADXL" header
    if ser.read(4) == b'ADXL':
        # Read rest of packet
        packet_data = ser.read(104)  # 108 - 4 header bytes
        
        # Unpack packet
        sensor_id, timestamp, sample_count = struct.unpack('<BIB', packet_data[:6])
        accel_data = struct.unpack('<48h', packet_data[6:102])  # 16*3 int16 values
        checksum = struct.unpack('<H', packet_data[102:104])[0]
        
        # Reshape accelerometer data
        samples = [(accel_data[i*3], accel_data[i*3+1], accel_data[i*3+2]) 
                  for i in range(16)]
        
        print(f"Sensor {sensor_id}: {len(samples)} samples at {timestamp}")
```

## Benefits

1. **Clean Data Stream**: No log messages mixed with sensor data
2. **Full Logging**: Complete ESP_LOG functionality via USB-JTAG
3. **High Performance**: 921,600 baud binary transmission
4. **Data Integrity**: Checksum verification for each packet
5. **Easy Debugging**: Separate logging channel for development

## Troubleshooting

### No Logs Visible
- Ensure USB cable is connected to ESP32-S3
- Use `idf.py monitor` (not regular serial monitor)
- Check USB-JTAG drivers are installed

### No Data on UART0
- Verify Pin 43 connection
- Check 921,600 baud rate setting
- Ensure receiver can handle binary data

### Mixed Data/Logs
- Verify sdkconfig changes are applied
- Rebuild and reflash firmware
- Check that no printf/cout statements remain in code
