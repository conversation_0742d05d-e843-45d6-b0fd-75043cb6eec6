{"configurations": [{"name": "ESP-IDF", "compilerPath": "${config:idf.toolsPath}/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc", "compileCommands": "${config:idf.buildPath}/compile_commands.json", "includePath": ["${config:idf.espIdfPath}/components/**", "${config:idf.espIdfPathWin}/components/**", "${workspaceFolder}/**"], "browse": {"path": ["${config:idf.espIdfPath}/components", "${config:idf.espIdfPathWin}/components", "${workspaceFolder}"], "limitSymbolsToIncludedHeaders": true}}], "version": 4}