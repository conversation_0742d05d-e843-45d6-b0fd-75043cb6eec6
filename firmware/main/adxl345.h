#ifndef ADXL345_H
#define ADXL345_H

#include <stdint.h>
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include "driver/spi_master.h"
#include "driver/gpio.h"
#include "esp_err.h"

//=== ADXL345 REGISTERS ===
#define ADXL345_REG_DEVID 0x00
#define ADXL345_REG_THRESH_TAP 0x1D
#define ADXL345_REG_OFSX 0x1E
#define ADXL345_REG_OFSY 0x1F
#define ADXL345_REG_OFSZ 0x20
#define ADXL345_REG_DUR 0x21
#define ADXL345_REG_LATENT 0x22
#define ADXL345_REG_WINDOW 0x23
#define ADXL345_REG_THRESH_ACT 0x24
#define ADXL345_REG_THRESH_INACT 0x25
#define ADXL345_REG_TIME_INACT 0x26
#define ADXL345_REG_ACT_INACT_CTL 0x27
#define ADXL345_REG_THRESH_FF 0x28
#define ADXL345_REG_TIME_FF 0x29
#define ADXL345_REG_TAP_AXES 0x2A
#define ADXL345_REG_ACT_TAP_STATUS 0x2B
#define ADXL345_REG_BW_RATE 0x2C
#define ADXL345_REG_POWER_CTL 0x2D
#define ADXL345_REG_INT_ENABLE 0x2E
#define ADXL345_REG_INT_MAP 0x2F
#define ADXL345_REG_INT_SOURCE 0x30
#define ADXL345_REG_DATA_FORMAT 0x31
#define ADXL345_REG_DATAX0 0x32
#define ADXL345_REG_DATAX1 0x33
#define ADXL345_REG_DATAY0 0x34
#define ADXL345_REG_DATAY1 0x35
#define ADXL345_REG_DATAZ0 0x36
#define ADXL345_REG_DATAZ1 0x37
#define ADXL345_REG_FIFO_CTL 0x38
#define ADXL345_REG_FIFO_STATUS 0x39

// ADXL345 expected device ID
#define ADXL345_DEVICE_ID 0xE5

//=== ADXL345 BIT DEFINES ===
// For read/write over SPI:
#define ADXL345_SPI_READ_BIT 0x80
#define ADXL345_SPI_MB_BIT 0x40 // Multi-byte

// BW_RATE register settings for 3200 Hz (0x0F)
#define ADXL345_RATE_3200HZ 0x0F

// DATA_FORMAT register bits:
#define ADXL345_RANGE_16G 0x03
#define ADXL345_FULL_RES_BIT (1 << 3) // Full-resolution mode

// POWER_CTL register:
#define ADXL345_MEASURE_BIT (1 << 3)

// INT_ENABLE bits:
#define ADXL345_INT_DATA_READY (1 << 7) // Data Ready interrupt

// INT_MAP bits:
#define ADXL345_INT1_DRDY 0x00 // Map Data Ready to INT1 (default)

//=== TYPES ===

// Each ADXL345 sensor instance:
typedef struct
{
    spi_device_handle_t spi_hdl;    // SPI device handle for this sensor
    gpio_num_t cs_io;               // CS pin (still useful for logs/debug)
    gpio_num_t int_pin;             // INT1 pin connected to this sensor
    SemaphoreHandle_t data_sem;     // Counting semaphore given in ISR
    volatile uint32_t sample_count; // Total number of samples read so far
} adxl345_dev_t;

//=== FUNCTION PROTOTYPES ===

/**
 * @brief Initialize SPI bus for ADXL345 communication
 * @param spi_host SPI host to use (e.g., SPI2_HOST)
 * @param miso_pin MISO pin number
 * @param mosi_pin MOSI pin number  
 * @param sclk_pin SCLK pin number
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t adxl345_spi_bus_init(spi_host_device_t spi_host, int miso_pin, int mosi_pin, int sclk_pin);

/**
 * @brief Add ADXL345 device to SPI bus
 * @param dev Pointer to ADXL345 device structure
 * @param spi_host SPI host to use
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t adxl345_add_device(adxl345_dev_t *dev, spi_host_device_t spi_host);

/**
 * @brief Initialize ADXL345 sensor
 * @param dev Pointer to ADXL345 device structure
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t adxl345_init(adxl345_dev_t *dev);

/**
 * @brief Setup GPIO interrupt for ADXL345 data ready signal
 * @param dev Pointer to ADXL345 device structure
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t adxl345_setup_interrupt(adxl345_dev_t *dev);

/**
 * @brief Create ADXL345 data processing task
 * @param dev Pointer to ADXL345 device structure
 * @param task_name Name for the task
 * @param stack_size Stack size for the task
 * @param priority Task priority
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t adxl345_create_data_task(adxl345_dev_t *dev, const char *task_name, uint32_t stack_size, UBaseType_t priority);

/**
 * @brief Create ADXL345 rate monitoring task
 * @param dev Pointer to ADXL345 device structure
 * @param task_name Name for the task
 * @param stack_size Stack size for the task
 * @param priority Task priority
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t adxl345_create_rate_task(adxl345_dev_t *dev, const char *task_name, uint32_t stack_size, UBaseType_t priority);

#endif // ADXL345_H
