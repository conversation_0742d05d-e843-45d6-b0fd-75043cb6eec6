menu "Firmware Configuration"

    config ENABLE_MICROSD_LOGGING
        bool "Enable MicroSD Card Logging"
        default n
        help
            Enable logging of sensor data to microSD card in JSON and CSV format.
            When enabled, the firmware will initialize an SD card connected via SPI
            and log data in the same format as labrecord.

    config MICROSD_SPI_HOST
        int "SPI Host for MicroSD Card"
        depends on ENABLE_MICROSD_LOGGING
        default 1
        range 1 2
        help
            SPI host number to use for microSD card communication.
            SPI2_HOST = 1 (HSPI), SPI3_HOST = 2 (VSPI)
            Note: SPI1_HOST = 0 is typically reserved for flash memory.

    config MICROSD_PIN_MISO
        int "MicroSD MISO Pin"
        depends on ENABLE_MICROSD_LOGGING
        default 37
        help
            GPIO pin number for SPI MISO (Master In Slave Out) signal.

    config MICROSD_PIN_MOSI
        int "MicroSD MOSI Pin"
        depends on ENABLE_MICROSD_LOGGING
        default 35
        help
            GPIO pin number for SPI MOSI (Master Out Slave In) signal.

    config MI<PERSON><PERSON>D_PIN_CLK
        int "MicroSD CLK Pin"
        depends on ENABLE_MICROSD_LOGGING
        default 36
        help
            GPIO pin number for SPI CLK (Clock) signal.

    config MICROSD_PIN_CS
        int "MicroSD CS Pin"
        depends on ENABLE_MICROSD_LOGGING
        default 34
        help
            GPIO pin number for SPI CS (Chip Select) signal.

    config MICROSD_BURST_QUEUE_SIZE
        int "MicroSD Burst Queue Size"
        depends on ENABLE_MICROSD_LOGGING
        default 10
        range 5 50
        help
            Number of sensor data bursts that can be queued for SD card logging.
            Each burst contains up to BURST_SAMPLES (32) samples.
            Larger values provide more buffering but use more memory.

endmenu
