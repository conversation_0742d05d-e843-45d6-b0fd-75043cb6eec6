/*
 * Example: Dual GY-291 ADXL345 @ 3200 Hz, full-resolution ±16 g on ESP32-S3 DevKitC-1
 *
 * - SPI bus on SPI2_HOST
 * - SCLK = GPIO 15
 * - MOSI (SDA) = GPIO 17
 * - MISO (SDO) = GPIO 18
 *
 * Sensor 1:
 * - CS1 = GPIO 5
 * - INT1 = GPIO 12 (Data-Ready interrupt)
 *
 * Sensor 2:
 * - CS2 = GPIO 6
 * - INT2 = GPIO 11 (Data-Ready interrupt)
 *
 * This firmware:
 *  1. Initializes SPI bus
 *  2. Attempts to initialize both ADXL345 sensors
 *  3. Supports operation with one or both sensors connected
 *  4. Aborts after 1 second if no sensors are detected
 *  5. Creates separate data and rate tasks for each connected sensor
 */

#include <stdio.h>
#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_err.h"
#include "adxl345.h"

#define TAG "FIRMWARE"

//=== GPIO / SPI DEFINES ===
#define SPI_HOST SPI2_HOST
#define PIN_SPI_SCLK 15
#define PIN_SPI_MOSI 17 // SDA
#define PIN_SPI_MISO 18 // SDO

// Sensor 1 pins
#define PIN_SPI_CS1 5
#define PIN_INT1 12 // Data-Ready (connected to ADXL345 sensor 1 INT1 pin)

// Sensor 2 pins
#define PIN_SPI_CS2 6
#define PIN_INT2 11 // Data-Ready (connected to ADXL345 sensor 2 INT1 pin)

//=== IMPLEMENTATION ===

/**
 * @brief Helper function to initialize a single ADXL345 sensor
 */
static esp_err_t init_sensor(adxl345_dev_t *dev, const char *sensor_name)
{
    esp_err_t ret;

    // Add the device to the SPI bus (creates spi_hdl)
    ret = adxl345_add_device(dev, SPI_HOST);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "Could not add %s to SPI bus: %s", sensor_name, esp_err_to_name(ret));
        return ret;
    }

    // Setup GPIO interrupt and semaphore
    ret = adxl345_setup_interrupt(dev);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "Failed to setup %s interrupt: %s", sensor_name, esp_err_to_name(ret));
        return ret;
    }

    // Initialize the sensor registers
    ret = adxl345_init(dev);
    if (ret != ESP_OK)
    {
        ESP_LOGW(TAG, "%s init failed: %s", sensor_name, esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "%s initialized successfully", sensor_name);
    return ESP_OK;
}

/**
 * @brief Main entry: initialize SPI, set up ADXL345 sensors, and start tasks.
 */
void app_main(void)
{
    esp_err_t ret;

    // 1) Initialize SPI bus
    ret = adxl345_spi_bus_init(SPI_HOST, PIN_SPI_MISO, PIN_SPI_MOSI, PIN_SPI_SCLK);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "SPI bus init failed, halting.");
        return;
    }
    vTaskDelay(pdMS_TO_TICKS(10));

    // 2) Set up ADXL345 sensor instances
    static adxl345_dev_t sensor1 = {
        .spi_hdl = NULL,
        .cs_io = PIN_SPI_CS1,
        .int_pin = PIN_INT1,
        .data_sem = NULL,
        .sample_count = 0};

    static adxl345_dev_t sensor2 = {
        .spi_hdl = NULL,
        .cs_io = PIN_SPI_CS2,
        .int_pin = PIN_INT2,
        .data_sem = NULL,
        .sample_count = 0};

    // 3) Try to initialize both sensors
    bool sensor1_ok = false;
    bool sensor2_ok = false;

    ESP_LOGI(TAG, "Attempting to initialize sensors...");

    // Try sensor 1
    if (init_sensor(&sensor1, "Sensor 1") == ESP_OK)
    {
        sensor1_ok = true;
    }

    // Try sensor 2
    if (init_sensor(&sensor2, "Sensor 2") == ESP_OK)
    {
        sensor2_ok = true;
    }

    // Check if at least one sensor is working
    if (!sensor1_ok && !sensor2_ok)
    {
        ESP_LOGE(TAG, "No sensors detected. Sleeping for 1 second before aborting...");
        vTaskDelay(pdMS_TO_TICKS(1000));
        ESP_LOGE(TAG, "Aborting - no sensors found.");
        return;
    }

    ESP_LOGI(TAG, "Sensor status: Sensor1=%s, Sensor2=%s",
             sensor1_ok ? "OK" : "FAILED",
             sensor2_ok ? "OK" : "FAILED");

    // 4) Create tasks for working sensors
    if (sensor1_ok)
    {
        // Create DataTask for sensor 1 (higher priority)
        ret = adxl345_create_data_task(&sensor1, "sensor1_data", 4096, configMAX_PRIORITIES - 2);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to create DataTask for Sensor 1.");
            return;
        }

        // Create RateTask for sensor 1 (lower priority)
        ret = adxl345_create_rate_task(&sensor1, "sensor1_stats", 4096, 5);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to create RateTask for Sensor 1.");
            return;
        }
        ESP_LOGI(TAG, "Tasks created for Sensor 1");
    }

    if (sensor2_ok)
    {
        // Create DataTask for sensor 2 (higher priority)
        ret = adxl345_create_data_task(&sensor2, "sensor2_data", 4096, configMAX_PRIORITIES - 2);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to create DataTask for Sensor 2.");
            return;
        }

        // Create RateTask for sensor 2 (lower priority)
        ret = adxl345_create_rate_task(&sensor2, "sensor2_stats", 4096, 5);
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to create RateTask for Sensor 2.");
            return;
        }
        ESP_LOGI(TAG, "Tasks created for Sensor 2");
    }

    ESP_LOGI(TAG, "Initialization complete. Waiting for data from %d sensor(s)...",
             (sensor1_ok ? 1 : 0) + (sensor2_ok ? 1 : 0));
}
