/*
 * Example: GY-291 ADXL345 @ 3200 Hz, full-resolution ±16 g on ESP32-S3 DevKitC-1
 *
 * - SPI bus on SPI2_HOST
 * - SCLK = GPIO 15
 * - MOSI (SDA) = GPIO 17
 * - MISO (SDO) = GPIO 18
 * - CS = GPIO 5
 * - INT1 = GPIO 11 (Data-Ready interrupt)
 * - INT2 = GPIO 12 (unused here)
 *
 * This proof-of-concept:
 *  1. Initializes SPI
 *  2. Initializes ADXL345 (verify WHO_AM_I, set full-resolution ±16 g, 3200 Hz, enable Data-Ready interrupt)
 *  3. Sets up a counting semaphore and two FreeRTOS tasks:
 *       • DataTask: waits on the semaphore, reads 6 bytes of accel data each interrupt, increments a counter.
 *       • RateTask: every 1 s, prints how many samples arrived in the last second.
 *  4. ISR on INT1 simply "gives" the semaphore.
 *
 * To add a second sensor later, you would:
 *   – Define a second adxl345_dev_t with its own .cs_io and .int_pin fields.
 *   – Call adxl345_init() again (after adding the device to the same SPI bus).
 *   – Create a second semaphore + DataTask/RateTask pair (or multiplex in one task).
 */

#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_err.h"
#include "adxl345.h"

#define TAG "FIRMWARE"

//=== GPIO / SPI DEFINES ===
#define SPI_HOST SPI2_HOST
#define PIN_SPI_SCLK 15
#define PIN_SPI_MOSI 17 // SDA
#define PIN_SPI_MISO 18 // SDO
#define PIN_SPI_CS 5
#define PIN_INT1 12 // Data-Ready (connected to ADXL345 INT1 pin)
#define PIN_INT2 11 // (unused in this example)

//=== IMPLEMENTATION ===

/**
 * @brief Main entry: initialize SPI, set up one ADXL345, and start tasks.
 */
void app_main(void)
{
    esp_err_t ret;

    // 1) Initialize SPI bus
    ret = adxl345_spi_bus_init(SPI_HOST, PIN_SPI_MISO, PIN_SPI_MOSI, PIN_SPI_SCLK);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "SPI bus init failed, halting.");
        return;
    }
    vTaskDelay(pdMS_TO_TICKS(10));

    // 2) Set up ADXL345 instance
    static adxl345_dev_t accel = {
        .spi_hdl = NULL,
        .cs_io = PIN_SPI_CS,
        .int_pin = PIN_INT1,
        .data_sem = NULL,
        .sample_count = 0};

    // 2a) Add the device to the SPI bus (creates spi_hdl)
    ret = adxl345_add_device(&accel, SPI_HOST);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Could not add ADXL345 to SPI bus.");
        return;
    }

    // 2b) Setup GPIO interrupt and semaphore
    ret = adxl345_setup_interrupt(&accel);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to setup ADXL345 interrupt.");
        return;
    }

    // 2c) Initialize the sensor registers
    ret = adxl345_init(&accel);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "ADXL345 init failed.");
        return;
    }

    // 3) Create the DataTask (higher priority, so it services interrupts promptly)
    ret = adxl345_create_data_task(&accel, "adxl345_data", 4096, configMAX_PRIORITIES - 2);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to create DataTask.");
        return;
    }

    // 4) Create the RateTask (lower priority)
    ret = adxl345_create_rate_task(&accel, "stats", 4096, 5);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to create RateTask.");
        return;
    }

    ESP_LOGI(TAG, "Initialization complete. Waiting for data...");
}
